import asyncio
import logging
import time
import weakref
from contextlib import contextmanager, asynccontextmanager
from dataclasses import dataclass
from typing import Optional, Generator, AsyncGenerator, TypeVar, runtime_checkable, Protocol, Any, Dict

import asyncpg
import sqlalchemy.exc as sa_exc

from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, create_async_engine
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy import create_engine, text
from sqlalchemy.engine.url import URL

# Type helpers
T = TypeVar("T")


@dataclass
class DatabaseConfig:
    """Database connection configuration"""
    pool_size: int = 20
    max_overflow: int = 10
    pool_recycle: int = 3600
    pool_timeout: int = 60  # Standardized for both sync/async
    pool_pre_ping: bool = True
    isolation_level: str = "READ COMMITTED"

    # Connection-specific timeouts
    connect_timeout: int = 30
    keepalives: int = 1
    keepalives_idle: int = 30
    keepalives_interval: int = 10
    keepalives_count: int = 5

    @property
    def sync_connect_args(self) -> Dict[str, Any]:
        return {
            "connect_timeout": self.connect_timeout,
            "keepalives": self.keepalives,
            "keepalives_idle": self.keepalives_idle,
            "keepalives_interval": self.keepalives_interval,
            "keepalives_count": self.keepalives_count,
        }

    @property
    def async_connect_args(self) -> Dict[str, Any]:
        # asyncpg uses slightly different parameter names
        return {
            "timeout": self.connect_timeout,
        }

# -------------------------
# Protocol (for typing)
# -------------------------
@runtime_checkable
class SupportsSessionRecovery(Protocol):
    logger: logging.Logger
    closed: bool

    def update_schema(self, new_schema: str) -> "SupportsSessionRecovery": ...

class _HasLogger(Protocol):
    logger: logging.Logger

class _HasManager(Protocol):
    _manager: _HasLogger


# -------------------------
# Connection Recovery Mixin
# -------------------------
def _is_recoverable_error(exc: Exception) -> bool:
    # DBAPI-level or SQLAlchemy connection invalidation
    if isinstance(exc, sa_exc.DBAPIError):
        # SQLAlchemy marks connection invalidation in this flag
        if getattr(exc, "connection_invalidated", False):
            return True

    # asyncpg-specific errors that are recoverable
    if isinstance(exc, (
        asyncpg.exceptions.InterfaceError,
        asyncpg.exceptions.ConnectionDoesNotExistError,
        asyncpg.exceptions.InvalidSQLStatementNameError,  # example
    )):
        return True

    # Generic network/connection error heuristics
    if isinstance(exc, (ConnectionError, TimeoutError)):
        return True

    return False


class ConnectionRecoveryMixin(_HasLogger):
    """
    Mixin that offers detection of recoverable DB errors and helpers to
    recreate engines. Concrete managers must provide _create_sync_engine and
    _create_async_engine when applicable.
    """

    MAX_RETRIES: int = 3
    RETRY_DELAY: float = 1.0  # base seconds

    # For sync callables
    def _retry_sync(self, func, *args, **kwargs):
        last = None
        for attempt in range(self.MAX_RETRIES):
            try:
                return func(*args, **kwargs)
            except Exception as exc:
                last = exc
                if _is_recoverable_error(exc) and attempt < self.MAX_RETRIES - 1:
                    delay = self.RETRY_DELAY * (2 ** attempt)
                    self.logger.warning(f"[sync] recoverable error: {exc!r}. retrying in {delay}s (attempt {attempt+1})")
                    try:
                        if hasattr(self, "recreate_sync_engine"):
                            self.recreate_sync_engine()
                    except Exception as e2:
                        self.logger.warning(f"[sync] engine recreate failed: {e2!r}")
                    time.sleep(delay)
                    continue
                raise
        raise last

    # For async callables
    async def _retry_async(self, func, *args, **kwargs):
        last = None
        for attempt in range(self.MAX_RETRIES):
            try:
                return await func(*args, **kwargs)
            except Exception as exc:
                last = exc
                if _is_recoverable_error(exc) and attempt < self.MAX_RETRIES - 1:
                    delay = self.RETRY_DELAY * (2 ** attempt)
                    self.logger.warning(f"[async] recoverable error: {exc!r}. retrying in {delay}s (attempt {attempt+1})")
                    try:
                        if hasattr(self, "_recreate_async_engine"):
                            await self._recreate_async_engine()
                    except Exception as e2:
                        self.logger.warning(f"[async] engine recreate failed: {e2!r}")
                    await asyncio.sleep(delay)
                    continue
                raise
        raise last


# -------------------------
# Base session manager
# -------------------------
class BaseSessionManager:
    def __init__(self, entry: Any, schema: str, rw: bool = True, logger: Optional[logging.Logger] = None):
        self.entry = entry
        self.schema = schema
        self.rw = rw
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self.closed = False

    def update_schema(self, new_schema: str) -> "BaseSessionManager":
        if self.closed:
            raise RuntimeError("Manager is closed")
        self.schema = new_schema
        return self

    def close(self) -> None:
        raise NotImplementedError

    async def aclose(self) -> None:
        raise NotImplementedError


# -------------------------
# Retryable Session classes
# -------------------------
class RetryableSession(Session, _HasManager):
    """
    Subclass of SQLAlchemy Session that retries a single statement execution
    when recoverable errors occur. The session must be assigned `_manager`
    attribute (the PostgresSessionManager) when created.
    """

    # number of statement-level retries
    STMT_RETRIES = 2

    def execute(self, statement, *args, **kwargs):
        manager = getattr(self, "_manager", None)
        attempt = 0
        last = None
        while True:
            try:
                return super().execute(statement, *args, **kwargs)
            except Exception as exc:
                last = exc
                recoverable = False
                if _is_recoverable_error(exc):
                    recoverable = True
                elif isinstance(exc, sa_exc.DBAPIError) and getattr(exc, "connection_invalidated", False):
                    recoverable = True
                else:
                    recoverable = False

                if recoverable and attempt < self.STMT_RETRIES:
                    self.rollback()
                    attempt += 1
                    self._log_retry(exc, attempt)
                    # Recreate engine if manager provides helper
                    if manager and hasattr(manager, "recreate_sync_engine"):
                        try:
                            manager.recreate_sync_engine()
                            # rebind this session to new engine
                            if manager.engine is not None:
                                self.bind = manager.engine
                        except Exception as e2:
                            self._log_recreate_failure(e2)
                    continue
                raise

    def _log_retry(self, exc: Exception, attempt: int):
        try:
            logger = self._manager.logger if getattr(self, "_manager", None) else logging.getLogger(__name__)
            logger.warning(f"[RetryableSession] retrying statement after error (attempt {attempt}): {exc!r}")
        except Exception:
            pass

    def _log_recreate_failure(self, exc: Exception):
        try:
            logger = self._manager.logger if getattr(self, "_manager", None) else logging.getLogger(__name__)
            logger.warning(f"[RetryableSession] engine recreate failed: {exc!r}")
        except Exception:
            pass


class RetryableAsyncSession(AsyncSession, _HasManager):
    STMT_RETRIES = 2

    async def execute(self, statement, *args, **kwargs):
        manager = getattr(self, "_manager", None)
        attempt = 0
        last = None
        while True:
            try:
                return await super().execute(statement, *args, **kwargs)
            except Exception as exc:
                last = exc
                recoverable = False
                if _is_recoverable_error(exc):  # Use the global function consistently
                    recoverable = True
                elif isinstance(exc, sa_exc.DBAPIError) and getattr(exc, "connection_invalidated", False):
                    recoverable = True

                if recoverable and attempt < self.STMT_RETRIES:
                    try:
                        await self.rollback()
                    except Exception:
                        pass
                    attempt += 1
                    self._log_retry(exc, attempt)
                    # Recreate engine / rebind
                    if manager and hasattr(manager, "_recreate_async_engine"):
                        try:
                            await manager._recreate_async_engine()
                            if getattr(manager, "engine_async", None) is not None:
                                # Create a new session bound to the new engine rather than trying to rebind
                                # This ensures schema translation is properly applied
                                self.bind = manager.engine_async
                        except Exception as e2:
                            self._log_recreate_failure(e2)
                    continue
                raise

    def _log_retry(self, exc: Exception, attempt: int):
        try:
            logger = self._manager.logger if getattr(self, "_manager", None) else logging.getLogger(__name__)
            logger.warning(f"[RetryableAsyncSession] retrying statement after error (attempt {attempt}): {exc!r}")
        except Exception:
            pass

    def _log_recreate_failure(self, exc: Exception):
        try:
            logger = self._manager.logger if getattr(self, "_manager", None) else logging.getLogger(__name__)
            logger.warning(f"[RetryableAsyncSession] engine recreate failed: {exc!r}")
        except Exception:
            pass


# -------------------------
# Sync Session Manager
# -------------------------
class SyncSessionManager(BaseSessionManager):
    def __init__(
            self, entry: Any, schema: str, rw: bool = True,
            logger: Optional[logging.Logger] = None,
            config: Optional[DatabaseConfig] = None
    ):
        print(f"sync url first: {SyncSessionManager._db_url(self)}")
        super().__init__(entry, schema, rw, logger)
        self.config = config or DatabaseConfig()
        self.engine: Optional[Engine] = None
        print(f"sync url second: {SyncSessionManager._db_url(self)}")
        self._create_sync_engine()

    def _db_url(self) -> URL:
        return URL.create(
            drivername="postgresql+psycopg2",
            username=self.entry.username,
            password=self.entry.password,
            host=str(self.entry.custom_properties["DB_SERVER_NAME"]),
            port=self.entry.custom_properties["DB_SERVER_RW_PORT"] if self.rw else self.entry.custom_properties["DB_SERVER_RO_PORT"],
            database=str(self.entry.custom_properties["DB_NAME"]),
        )

    def _create_sync_engine(self) -> None:
        # Explicitly call SyncSessionManager's _db_url to avoid MRO issues
        sync_url = SyncSessionManager._db_url(self)
        self.engine = create_engine(
            sync_url,
            pool_size=self.config.pool_size,
            max_overflow=self.config.max_overflow,
            pool_recycle=self.config.pool_recycle,
            pool_timeout=self.config.pool_timeout,
            pool_pre_ping=self.config.pool_pre_ping,
            isolation_level=self.config.isolation_level,
            connect_args=self.config.sync_connect_args,
        ).execution_options(schema_translate_map={None: self.schema})

    def recreate_sync_engine(self) -> None:
        try:
            if self.engine is not None:
                try:
                    self.engine.dispose()
                except Exception:
                    pass
            self._create_sync_engine()
            self.logger.info("Sync engine recreated")
        except Exception as e:
            self.logger.warning(f"Failed to recreate sync engine: {e!r}")
            raise

    @contextmanager
    def session(self) -> Generator[Session, None, None]:
        if self.closed:
            raise RuntimeError("Manager closed")
        factory = sessionmaker(bind=self.engine, class_=RetryableSession, autoflush=False, expire_on_commit=False)
        sess: RetryableSession = factory() # type: ignore[assignment]
        # attach manager for retryable session behavior
        sess._manager = self
        try:
            yield sess
        except Exception:
            try:
                sess.rollback()
            except Exception as rollback_err:
                self.logger.warning(f"Error during rollback: {rollback_err}")
            raise
        finally:
            # Always close the session
            try:
                sess.close()
            except Exception as close_err:
                self.logger.warning(f"Error closing session: {close_err}")

    def close(self) -> None:
        if self.closed:
            return
        self.closed = True
        if self.engine is not None:
            try:
                self.engine.dispose()
            except Exception:
                self.logger.debug("Ignoring engine dispose error")
            finally:
                self.engine = None

    async def aclose(self) -> None:
        self.close()


# -------------------------
# Async Session Manager
# -------------------------
class AsyncSessionManager(BaseSessionManager):
    def __init__(
            self, entry: Any, schema: str, rw: bool = True,
            logger: Optional[logging.Logger] = None,
            config: Optional[DatabaseConfig] = None
    ):
        super().__init__(entry, schema, rw, logger)
        self.config = config or DatabaseConfig()
        self.engine_async: Optional[AsyncEngine] = None
        self._create_async_engine()

    def _db_url(self) -> URL:
        return URL.create(
            drivername="postgresql+asyncpg",
            username=self.entry.username,
            password=self.entry.password,
            host=str(self.entry.custom_properties["DB_SERVER_NAME"]),
            port=self.entry.custom_properties["DB_SERVER_RW_PORT"] if self.rw else self.entry.custom_properties["DB_SERVER_RO_PORT"],
            database=str(self.entry.custom_properties["DB_NAME"]),
        )

    def _create_async_engine(self) -> None:
        # Explicitly call AsyncSessionManager's _db_url to avoid MRO issues
        async_url = AsyncSessionManager._db_url(self)
        self.engine_async = create_async_engine(
            async_url,
            pool_size=self.config.pool_size,
            max_overflow=self.config.max_overflow,
            pool_recycle=self.config.pool_recycle,
            pool_timeout=self.config.pool_timeout,
            pool_pre_ping=self.config.pool_pre_ping,
            isolation_level=self.config.isolation_level,
            connect_args=self.config.async_connect_args,
        ).execution_options(schema_translate_map={None: self.schema})

    async def _recreate_async_engine(self) -> None:
        try:
            if self.engine_async is not None:
                try:
                    await self.engine_async.dispose()
                except Exception:
                    pass
            self._create_async_engine()
            self.logger.info("Async engine recreated")
        except Exception as e:
            self.logger.warning(f"Failed to recreate async engine: {e!r}")
            raise

    @asynccontextmanager
    async def async_session(self) -> AsyncGenerator[AsyncSession, None]:
        if self.closed:
            raise RuntimeError("Manager closed")
        factory = sessionmaker(bind=self.engine_async, class_=RetryableAsyncSession, expire_on_commit=False, autoflush=False)
        sess: RetryableAsyncSession = factory()  # type: ignore[assignment]
        # attach manager
        sess._manager = self
        try:
            yield sess
        except Exception:
            try:
                await sess.rollback()
            except Exception as rollback_err:
                self.logger.warning(f"Error during rollback: {rollback_err}")
            raise
        finally:
            # Always close the session
            try:
                await sess.close()
            except Exception as close_err:
                self.logger.warning(f"Error closing session: {close_err}")

    def close(self) -> None:
        """Synchronous cleanup - delegates to async cleanup when possible."""
        if self.closed:
            return

        # Try to run async cleanup if possible
        try:
            # Check if we're in an async context
            try:
                loop = asyncio.get_running_loop()
                # We're in an async context, but this is a sync method
                # Just mark as closed and let aclose handle the actual cleanup
                self.closed = True
                self.logger.warning("close() called in async context - use aclose() instead")
            except RuntimeError:
                # No running loop, we can safely run async cleanup
                asyncio.run(self.aclose())
        except Exception as err:
            self.logger.warning(f"Error during sync cleanup: {err}")
            # Fallback: just mark as closed
            self.closed = True

    async def aclose(self) -> None:
        if self.closed:
            return
        self.closed = True
        if self.engine_async is not None:
            try:
                await self.engine_async.dispose()
            except Exception:
                self.logger.debug("Ignoring async engine dispose error")
            finally:
                self.engine_async = None


# -------------------------
# Combined Postgres manager with retry wrappers
# -------------------------
class PostgresSessionManager(SyncSessionManager, AsyncSessionManager, ConnectionRecoveryMixin):
    _instances = weakref.WeakSet()

    def __init__(
            self, entry: Any, schema: str, rw: bool = True, logger: Optional[logging.Logger] = None,
            create_sync: bool = True, create_async: bool = True, max_retries: int = 3, retry_delay: float = 1.0
    ):
        # initialize base logger & settings first
        BaseSessionManager.__init__(self, entry, schema, rw, logger)
        self.MAX_RETRIES = max_retries
        self.RETRY_DELAY = retry_delay

        # conditional initialization for sync/async
        if create_sync:
            SyncSessionManager.__init__(self, entry, schema, rw, logger)
        else:
            # set attributes to None to avoid attribute errors in mixins/tools
            self.engine = None

        if create_async:
            AsyncSessionManager.__init__(self, entry, schema, rw, logger)
        else:
            self.engine_async = None

        self._instances.add(self)

    # -------------------------
    # Session-level retry contexts
    # -------------------------
    @contextmanager
    def session_with_retry(self, tx_retries: int = 2) -> Generator[Session, None, None]:
        """
        Synchronous context manager that retries the entire transaction block
        if recoverable errors occur (includes re-creating engine).
        Usage:
            with manager.session_with_retry() as sess:
                sess.execute(...)
        """
        attempt = 0
        last_exc = None
        while True:
            try:
                with self.session() as sess:
                    try:
                        yield sess
                        return
                    except Exception as e_tx:
                        sess.rollback()
                        last_exc = e_tx
                        if _is_recoverable_error(e_tx) and attempt < tx_retries:
                            self.logger.warning(f"[session_with_retry] transaction error {e_tx!r}, retrying transaction (attempt {attempt+1})")
                            attempt += 1
                            # recreate engine and continue to retry
                            self.recreate_sync_engine()
                            continue
                        raise
            except Exception as e_open:
                last_exc = e_open
                if _is_recoverable_error(e_open) and attempt < tx_retries:
                    self.logger.warning(f"[session_with_retry] session open error {e_open!r}, retrying session open (attempt {attempt+1})")
                    attempt += 1
                    self.recreate_sync_engine()
                    continue
                raise

    @asynccontextmanager
    async def async_session_with_retry(self, tx_retries: int = 2) -> AsyncGenerator[AsyncSession, None]:
        """
        Async context manager that retries the entire transaction block.
        Usage:
            async with manager.async_session_with_retry() as sess:
                await sess.execute(...)
        """
        attempt = 0
        last_exc = None
        while True:
            try:
                async with self.async_session() as sess:
                    try:
                        yield sess
                        return
                    except Exception as e_tx:
                        try:
                            await sess.rollback()
                        except Exception:
                            pass
                        last_exc = e_tx
                        if _is_recoverable_error(e_tx) and attempt < tx_retries:
                            self.logger.warning(f"[async_session_with_retry] transaction error {e_tx!r}, retrying transaction (attempt {attempt+1})")
                            attempt += 1
                            await self._recreate_async_engine()
                            continue
                        raise
            except Exception as e_open:
                last_exc = e_open
                if _is_recoverable_error(e_open) and attempt < tx_retries:
                    self.logger.warning(f"[async_session_with_retry] session open error {e_open!r}, retrying session open (attempt {attempt+1})")
                    attempt += 1
                    await self._recreate_async_engine()
                    continue
                raise

    # -------------------------
    # Schema update override to ensure engines get updated
    # -------------------------
    def update_schema(self, new_schema: str) -> "PostgresSessionManager":
        if self.closed:
            raise RuntimeError("Manager closed")
        self.schema = new_schema
        if getattr(self, "engine", None) is not None:
            self.engine = self.engine.execution_options(schema_translate_map={None: self.schema})
        if getattr(self, "engine_async", None) is not None:
            self.engine_async = self.engine_async.execution_options(schema_translate_map={None: self.schema})
        return self

    # -------------------------
    # Cleanup helpers
    # -------------------------
    def close(self) -> None:
        # closes sync engine and marks closed; async engine disposed in aclose
        if getattr(self, "engine", None) and not self.closed:
            try:
                self.engine.dispose()
            except Exception:
                self.logger.debug("Ignoring dispose errors on close")
            finally:
                self.engine = None
        self.closed = True

    async def aclose(self) -> None:
        if getattr(self, "engine_async", None) and not self.closed:
            try:
                await self.engine_async.dispose()
            except Exception:
                self.logger.debug("Ignoring dispose errors on aclose")
            finally:
                self.engine_async = None
        self.closed = True

    @classmethod
    async def cleanup_all(cls):
        instances = list(cls._instances)
        cls._instances.clear()
        for inst in instances:
            try:
                if getattr(inst, "engine_async", None):
                    await inst.aclose()
                if getattr(inst, "engine", None):
                    inst.close()
            except Exception as e:
                logging.getLogger(__name__).warning(f"Error cleaning up instance: {e!r}")


    @classmethod
    def create_sync_only(cls, entry: Any, schema: str, rw: bool = True,
                         logger: Optional[logging.Logger] = None, **kwargs) -> "PostgresSessionManager":
        """Factory method for sync-only session manager"""
        return cls(entry, schema, rw, logger, create_sync=True, create_async=False, **kwargs)

    @classmethod
    def create_async_only(cls, entry: Any, schema: str, rw: bool = True,
                          logger: Optional[logging.Logger] = None, **kwargs) -> "PostgresSessionManager":
        """Factory method for async-only session manager"""
        return cls(entry, schema, rw, logger, create_sync=False, create_async=True, **kwargs)

    @classmethod
    def create_dual(cls, entry: Any, schema: str, rw: bool = True,
                    logger: Optional[logging.Logger] = None, **kwargs) -> "PostgresSessionManager":
        """Factory method for both sync and async session manager"""
        return cls(entry, schema, rw, logger, create_sync=True, create_async=True, **kwargs)

    # Properties for easier access in DI containers
    @property
    def has_sync(self) -> bool:
        """Check if sync engine is available"""
        return self.engine is not None

    @property
    def has_async(self) -> bool:
        """Check if async engine is available"""
        return self.engine_async is not None

    @classmethod
    def get_all_instances(cls) -> list["PostgresSessionManager"]:
        """Get all active instances"""
        return list(cls._instances)

    # Add these methods to PostgresSessionManager for better monitoring

    def health_check(self) -> Dict[str, Any]:
        """Perform health check on available engines"""
        health = {
            "sync_engine": None,
            "async_engine": None,
            "closed": self.closed
        }

        if self.engine:
            try:
                with self.engine.connect() as conn:
                    result = conn.execute(text("SELECT 1")).scalar()
                    health["sync_engine"] = {"status": "healthy", "test_query": result == 1}
            except Exception as e:
                health["sync_engine"] = {"status": "unhealthy", "error": str(e)}

        return health

    async def async_health_check(self) -> Dict[str, Any]:
        """Perform async health check"""
        health = {
            "async_engine": None,
            "closed": self.closed
        }

        if self.engine_async:
            try:
                async with self.engine_async.connect() as conn:
                    result = await conn.execute(text("SELECT 1"))
                    health["async_engine"] = {"status": "healthy", "test_query": result.scalar() == 1}
            except Exception as e:
                health["async_engine"] = {"status": "unhealthy", "error": str(e)}

        return health

    def get_pool_status(self) -> Dict[str, Any]:
        """Get connection pool status for monitoring"""
        status = {}

        if self.engine and hasattr(self.engine.pool, 'status'):
            pool = self.engine.pool
            status["sync_pool"] = {
                "size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "invalidated": pool.invalidated()
            }

        if self.engine_async and hasattr(self.engine_async.pool, 'status'):
            pool = self.engine_async.pool
            status["async_pool"] = {
                "size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "invalidated": pool.invalidated()
            }

        return status

# -------------------------
# Usage examples
# -------------------------
# Sync:
# manager = PostgresSessionManager(entry, schema="public", create_sync=True, create_async=False)
# with manager.session_with_retry() as sess:
#     result = sess.execute(text("SELECT 1")).scalar()
#
# Async:
# manager = PostgresSessionManager(entry, schema="public", create_sync=False, create_async=True)
# async with manager.async_session_with_retry() as sess:
#     res = await sess.execute(text("SELECT 1"))
#     print(res.scalar())
