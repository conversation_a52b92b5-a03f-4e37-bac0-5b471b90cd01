# coding=utf-8
"""
Scalable Coordination System for JIRA Data Pipeline.

This module provides event-based coordination and dynamic scaling for producers
and consumers based on workload size using Fibonacci series scaling rules.
"""

import asyncio
import logging
from collections import defaultdict
from dataclasses import dataclass, field
from typing import Dict, Set, Callable, Any, Optional, Tuple, List
from enum import Enum


class QueueType(Enum):
    """Queue types for different processing stages"""
    ISSUES = "queue_issues"
    CHANGELOG = "queue_changelog"
    WORKLOG = "queue_worklog"
    COMMENT = "queue_comment"
    ISSUE_LINKS = "queue_issue_links"
    ISSUE = "queue_issue"
    UPSERT_ISSUE = "queue_upsert_issue"
    STATS = "queue_stats"


@dataclass
class ScalingConfig:
    """Configuration for dynamic scaling behavior"""
    max_producers: int = 15
    consumer_scale_threshold: int = 50
    min_consumers_per_queue: int = 1
    max_consumers_per_queue: int = 3
    fibonacci_thresholds: List[Tuple[int, int]] = field(default_factory=lambda: [
        (1597, 1), (2584, 2), (4181, 3), (6765, 4), (10946, 5),
        (17711, 6), (28657, 7), (46368, 8), (75025, 9), (121393, 10),
        (196418, 11), (317811, 12), (514229, 13), (832040, 14), (1346269, 15)
    ])


class FibonacciProducerScaler:
    """Handles Fibonacci-based producer scaling logic"""
    
    def __init__(self, config: ScalingConfig = None):
        self.config = config or ScalingConfig()
        self.logger = logging.getLogger(__name__)
    
    def get_producer_count(self, record_count: int) -> int:
        """
        Determine number of producers based on record count using Fibonacci series.
        
        Args:
            record_count: Total number of records to process
            
        Returns:
            Number of producers to spawn (1-15)
        """
        if record_count <= 0:
            return 1
            
        for threshold, producers in self.config.fibonacci_thresholds:
            if record_count <= threshold:
                self.logger.info(f"Record count {record_count} -> {producers} producers")
                return producers
        
        self.logger.info(f"Record count {record_count} -> {self.config.max_producers} producers (max)")
        return self.config.max_producers
    
    def get_batch_parameters(self, record_count: int) -> Tuple[int, int]:
        """
        Calculate optimal batch parameters for split_jql_by_count.
        
        Args:
            record_count: Total number of records
            
        Returns:
            Tuple of (max_batch_size, num_batches)
        """
        num_producers = self.get_producer_count(record_count)
        
        # Calculate optimal batch size
        if record_count <= 1000:
            max_batch_size = record_count
        else:
            max_batch_size = max(100, record_count // num_producers)
        
        # Ensure we don't exceed reasonable limits
        max_batch_size = min(max_batch_size, 5000)
        
        self.logger.debug(f"Batch parameters: max_batch_size={max_batch_size}, num_batches={num_producers}")
        return max_batch_size, num_producers


class ScalableCoordinationManager:
    """
    Manages event-based coordination between producers and consumers with dynamic scaling.
    
    This replaces the None sentinel-based system with proper asyncio coordination.
    """
    
    def __init__(self, config: ScalingConfig = None):
        self.config = config or ScalingConfig()
        self.logger = logging.getLogger(__name__)
        
        # Core coordination events
        self.producers_completed = asyncio.Event()
        self.all_consumers_ready = asyncio.Condition()
        self.processing_complete = asyncio.Event()
        self.shutdown_requested = asyncio.Event()
        
        # Producer/Consumer tracking
        self.active_producers: Set[str] = set()
        self.active_consumers: Dict[str, Set[str]] = defaultdict(set)
        self.producer_count = 0
        self.consumer_counts: Dict[str, int] = defaultdict(int)
        
        # Queue monitoring
        self.queue_sizes: Dict[str, int] = defaultdict(int)
        self.queue_refs: Dict[str, asyncio.Queue] = {}
        
        # Processing triggers
        self.consume_issue_complete = asyncio.Event()
        self.immediate_processing = asyncio.Event()
        
        # Locks for thread safety
        self._producer_lock = asyncio.Lock()
        self._consumer_lock = asyncio.Lock()
    
    async def register_producer(self, producer_id: str) -> None:
        """Register a new producer"""
        async with self._producer_lock:
            self.active_producers.add(producer_id)
            self.producer_count = len(self.active_producers)
            self.logger.debug(f"Registered producer {producer_id}. Total: {self.producer_count}")
    
    async def unregister_producer(self, producer_id: str) -> None:
        """Unregister a completed producer"""
        async with self._producer_lock:
            self.active_producers.discard(producer_id)
            self.producer_count = len(self.active_producers)
            self.logger.debug(f"Unregistered producer {producer_id}. Remaining: {self.producer_count}")
            
            # Signal completion when all producers are done
            if self.producer_count == 0:
                self.producers_completed.set()
                self.logger.info("All producers completed - signaling consumers")
    
    async def register_consumer(self, queue_name: str, consumer_id: str) -> None:
        """Register a new consumer for a specific queue"""
        async with self._consumer_lock:
            self.active_consumers[queue_name].add(consumer_id)
            self.consumer_counts[queue_name] = len(self.active_consumers[queue_name])
            self.logger.debug(f"Registered consumer {consumer_id} for {queue_name}. Total: {self.consumer_counts[queue_name]}")
    
    async def unregister_consumer(self, queue_name: str, consumer_id: str) -> None:
        """Unregister a completed consumer"""
        async with self._consumer_lock:
            self.active_consumers[queue_name].discard(consumer_id)
            self.consumer_counts[queue_name] = len(self.active_consumers[queue_name])
            self.logger.debug(f"Unregistered consumer {consumer_id} from {queue_name}. Remaining: {self.consumer_counts[queue_name]}")
    
    def register_queue(self, queue_name: str, queue_ref: asyncio.Queue) -> None:
        """Register a queue reference for monitoring"""
        self.queue_refs[queue_name] = queue_ref
        self.logger.debug(f"Registered queue {queue_name}")
    
    def get_queue_size(self, queue_name: str) -> int:
        """Get current size of a queue"""
        if queue_name in self.queue_refs:
            return self.queue_refs[queue_name].qsize()
        return 0
    
    async def should_terminate_consumer(self, queue_name: str) -> bool:
        """
        Check if a consumer should terminate.
        
        Consumer should terminate when:
        1. All producers are completed AND
        2. The queue is empty
        """
        producers_done = self.producers_completed.is_set()
        queue_empty = self.get_queue_size(queue_name) == 0
        
        should_terminate = producers_done and queue_empty
        
        if should_terminate:
            self.logger.debug(f"Consumer for {queue_name} should terminate: producers_done={producers_done}, queue_empty={queue_empty}")


        
        return should_terminate
    
    async def wait_for_producers_completion(self) -> None:
        """Wait for all producers to complete"""
        await self.producers_completed.wait()
        self.logger.info("All producers have completed")
    
    async def signal_consume_issue_complete(self) -> None:
        """Signal that consume_issue processing is complete"""
        self.consume_issue_complete.set()
        self.immediate_processing.set()
        self.logger.info("consume_issue processing completed - triggering immediate processing")
    
    async def wait_for_processing_trigger(self, batch_threshold: int = 10000) -> bool:
        """
        Wait for either immediate processing trigger or batch threshold.
        
        Returns:
            True if immediate processing was triggered, False if batch threshold reached
        """
        # Create tasks for both conditions
        immediate_task = asyncio.create_task(self.immediate_processing.wait())
        
        try:
            # Wait for immediate trigger
            await immediate_task
            self.logger.info("Immediate processing triggered")
            return True
        except asyncio.CancelledError:
            self.logger.debug("Processing trigger wait cancelled")
            return False
    
    async def should_scale_consumers(self, queue_name: str) -> Optional[str]:
        """
        Determine if consumers should be scaled up or down.
        
        Returns:
            'up' if should scale up, 'down' if should scale down, None if no change needed
        """
        queue_size = self.get_queue_size(queue_name)
        current_consumers = self.consumer_counts[queue_name]
        
        if queue_size > self.config.consumer_scale_threshold and current_consumers < self.config.max_consumers_per_queue:
            return 'up'
        elif queue_size <= self.config.consumer_scale_threshold and current_consumers > self.config.min_consumers_per_queue:
            return 'down'
        
        return None
    
    async def request_shutdown(self) -> None:
        """Request graceful shutdown of all components"""
        self.shutdown_requested.set()
        self.logger.info("Shutdown requested for coordination manager")
    
    def is_shutdown_requested(self) -> bool:
        """Check if shutdown has been requested"""
        return self.shutdown_requested.is_set()


class DynamicConsumerManager:
    """
    Manages dynamic scaling of consumers for specialized queues.

    Spawns additional consumers when queue size exceeds threshold,
    scales down when queue size is manageable.
    """

    def __init__(self, coordination_manager: ScalableCoordinationManager):
        self.coordination_manager = coordination_manager
        self.logger = logging.getLogger(__name__)
        self.consumer_tasks: Dict[str, List[asyncio.Task]] = defaultdict(list)
        self.scaling_lock = asyncio.Lock()

    async def manage_queue_scaling(
        self,
        queue_name: str,
        consumer_factory: Callable,
        consumer_args: tuple,
        consumer_kwargs: dict,
        check_interval: float = 5.0
    ) -> None:
        """
        Monitor and manage consumer scaling for a specific queue.

        Args:
            queue_name: Name of the queue to monitor
            consumer_factory: Function to create new consumers
            consumer_args: Arguments for consumer factory
            consumer_kwargs: Keyword arguments for consumer factory
            check_interval: How often to check queue size (seconds)
        """
        self.logger.info(f"Starting dynamic scaling management for {queue_name}")

        try:
            # Always start with at least one consumer
            await self._ensure_minimum_consumers(queue_name, consumer_factory, consumer_args, consumer_kwargs)

            while not self.coordination_manager.is_shutdown_requested():
                scaling_action = await self.coordination_manager.should_scale_consumers(queue_name)

                if scaling_action == 'up':
                    await self._scale_up_consumers(queue_name, consumer_factory, consumer_args, consumer_kwargs)
                elif scaling_action == 'down':
                    await self._scale_down_consumers(queue_name)

                # Check if all producers are done and queue is empty
                if await self.coordination_manager.should_terminate_consumer(queue_name):
                    self.logger.info(f"Terminating all consumers for {queue_name}")
                    await self._terminate_all_consumers(queue_name)
                    break

                await asyncio.sleep(check_interval)

        except asyncio.CancelledError:
            self.logger.info(f"Dynamic scaling cancelled for {queue_name}")
            await self._terminate_all_consumers(queue_name)
        except Exception as e:
            self.logger.error(f"Error in dynamic scaling for {queue_name}: {e}")
            await self._terminate_all_consumers(queue_name)

    async def _ensure_minimum_consumers(
        self,
        queue_name: str,
        consumer_factory: Callable,
        consumer_args: tuple,
        consumer_kwargs: dict
    ) -> None:
        """Ensure at least minimum number of consumers are running"""
        async with self.scaling_lock:
            current_count = len(self.consumer_tasks[queue_name])
            min_consumers = self.coordination_manager.config.min_consumers_per_queue

            while current_count < min_consumers:
                consumer_id = f"{queue_name}_consumer_{current_count + 1}"
                self.logger.info(f"Starting initial consumer {consumer_id} for {queue_name}")

                # Create new consumer task
                task = asyncio.create_task(
                    consumer_factory(*consumer_args, **consumer_kwargs),
                    name=consumer_id
                )

                self.consumer_tasks[queue_name].append(task)
                await self.coordination_manager.register_consumer(queue_name, consumer_id)
                current_count += 1

    async def _scale_up_consumers(
        self,
        queue_name: str,
        consumer_factory: Callable,
        consumer_args: tuple,
        consumer_kwargs: dict
    ) -> None:
        """Scale up consumers for a queue"""
        async with self.scaling_lock:
            current_count = len(self.consumer_tasks[queue_name])
            if current_count >= self.coordination_manager.config.max_consumers_per_queue:
                return

            consumer_id = f"{queue_name}_consumer_{current_count + 1}"
            self.logger.info(f"Scaling up: Adding consumer {consumer_id} for {queue_name}")

            # Create new consumer task
            task = asyncio.create_task(
                consumer_factory(*consumer_args, **consumer_kwargs),
                name=consumer_id
            )

            self.consumer_tasks[queue_name].append(task)
            await self.coordination_manager.register_consumer(queue_name, consumer_id)

    async def _scale_down_consumers(self, queue_name: str) -> None:
        """Scale down consumers for a queue"""
        async with self.scaling_lock:
            if len(self.consumer_tasks[queue_name]) <= self.coordination_manager.config.min_consumers_per_queue:
                return

            # Cancel the most recent consumer
            task_to_cancel = self.consumer_tasks[queue_name].pop()
            consumer_id = task_to_cancel.get_name()

            self.logger.info(f"Scaling down: Removing consumer {consumer_id} for {queue_name}")

            task_to_cancel.cancel()
            await self.coordination_manager.unregister_consumer(queue_name, consumer_id)

    async def _terminate_all_consumers(self, queue_name: str) -> None:
        """Terminate all consumers for a queue"""
        async with self.scaling_lock:
            tasks = self.consumer_tasks[queue_name].copy()
            self.consumer_tasks[queue_name].clear()

            for task in tasks:
                if not task.done():
                    task.cancel()
                    consumer_id = task.get_name()
                    await self.coordination_manager.unregister_consumer(queue_name, consumer_id)

            self.logger.info(f"Terminated all consumers for {queue_name}")


# Global instances for use across the application
default_scaling_config = ScalingConfig()
fibonacci_scaler = FibonacciProducerScaler(default_scaling_config)
coordination_manager = ScalableCoordinationManager(default_scaling_config)
dynamic_consumer_manager = DynamicConsumerManager(coordination_manager)
