flowchart TD
    classDef queue fill:#fce5cd,stroke:#d35400,stroke-width:2px,rx:10,ry:10
    classDef worker fill:#d5f5e3,stroke:#1e8449,stroke-width:2px,rx:5,ry:5
    classDef group fill:#d6eaf8,stroke:#154360,stroke-width:2px,rx:5,ry:5
    classDef db fill:#fadbd8,stroke:#922b21,stroke-width:3px,rx:10,ry:10
    classDef stage fill:#e8f8f5,stroke:#1abc9c,stroke-width:1px,stroke-dasharray: 5 5
    
    subgraph S1 ["🎯 Stage 1: Issue Collection"]
        direction TB
        P[📥 Producers<br/>Thread Pool: 1-10<br/>Fetch JIRA Issues]:::worker 
        --> Q1[⚡ Main Priority Queue<br/>📊 Issue Objects<br/>FIFO + Priority]:::queue
        Q1 --> C[🔄 Consumers<br/>Thread Pool: 1-n<br/>Parse & Route Issues]:::worker
    end
    
    subgraph S2 ["🔀 Stage 2: Fan-out Processing"]
        direction TB
        subgraph Queues ["Specialized Queues"]
            QA[📋 Issue Queue<br/>Core Issue Data]:::queue
            QB[⏱️ Worklog Queue<br/>Time Tracking]:::queue  
            QC[📝 Changelog Queue<br/>History Data]:::queue
            QD[🔗 IssueLinks Queue<br/>Relationship Data]:::queue
            QE[💬 Comments Queue<br/>Discussion Data]:::queue
        end
        
        subgraph Workers ["Processing Workers"]
            CA[🏭 process_issue<br/>Transform Core Data]:::worker
            CB[🏭 process_worklog<br/>Parse Time Entries]:::worker
            CC[🏭 process_changelog<br/>Track Changes]:::worker
            CD[🏭 process_issue_links<br/>Map Relations]:::worker
            CE[🏭 consume_comments<br/>Extract Discussions]:::worker
        end
    end
    
    subgraph S3 ["💾 Stage 3: Data Persistence"]
        direction TB
        QF[🎯 Final Upsert Queue<br/>📤 Processed Records<br/>Batch Operations]:::queue
        --> PgDB[🐘 PostgreSQL<br/>📊 Data Warehouse<br/>🔄 UPSERT Operations]:::db
    end
    
    %% Stage 1 to Stage 2 connections
    C -.->|Route by Type| QA
    C -.->|Route by Type| QB  
    C -.->|Route by Type| QC
    C -.->|Route by Type| QD
    C -.->|Route by Type| QE
    
    %% Stage 2 internal connections
    QA --> CA
    QB --> CB
    QC --> CC
    QD --> CD
    QE --> CE
    
    %% Stage 2 to Stage 3 connections
    CA -.->|Transformed Data| QF
    CB -.->|Processed Logs| QF
    CC -.->|Change Records| QF
    CD -.->|Link Mappings| QF
    CE -.->|Comment Data| QF
    
    %% Add metrics/monitoring boxes
    M1[📊 Metrics<br/>• Queue Depths<br/>• Processing Rates<br/>• Error Counts]:::group
    M1 -.-> S1
    M1 -.-> S2  
    M1 -.-> S3
    
    %% Error handling
    E1[⚠️ Error Handling<br/>• Dead Letter Queues<br/>• Retry Logic<br/>• Circuit Breakers]:::group
    E1 -.-> S2