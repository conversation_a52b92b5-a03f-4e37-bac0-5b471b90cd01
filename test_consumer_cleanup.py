#!/usr/bin/env python3
"""
Test script to verify consumer cleanup works correctly.
"""

import asyncio
import logging
from unittest.mock import MagicMock

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_consumer_cleanup():
    """Test that consumers are properly cleaned up when consume_issues completes."""
    
    # Import the coordination manager
    from dags.data_pipeline.scalable_coordination import ScalableCoordinationManager
    
    # Create coordination manager with logger
    coordination_manager = ScalableCoordinationManager()
    coordination_manager.logger = logger
    
    # Register specialized consumers
    specialized_queues = ["queue_changelog", "queue_worklog", "queue_comment", "queue_issue_links", "queue_issue"]
    
    for queue_name in specialized_queues:
        # Mock empty queue
        mock_queue = MagicMock()
        mock_queue.qsize.return_value = 0
        coordination_manager.register_queue(queue_name, mock_queue)
        
        # Register a consumer
        await coordination_manager.register_consumer(queue_name, f"{queue_name}_consumer_1")
    
    # Mock upsert queue
    mock_upsert_queue = MagicMock()
    mock_upsert_queue.qsize.return_value = 0
    coordination_manager.register_queue("queue_upsert_issue", mock_upsert_queue)
    
    print("=== Initial State ===")
    print(f"consume_issues_complete: {coordination_manager.is_consume_issues_complete()}")
    print(f"are_all_specialized_consumers_done: {coordination_manager.are_all_specialized_consumers_done()}")
    
    for queue_name in specialized_queues:
        should_terminate = await coordination_manager.should_terminate_consumer(queue_name)
        print(f"{queue_name} should_terminate: {should_terminate}")
    
    upsert_should_terminate = await coordination_manager.should_terminate_consumer("queue_upsert_issue")
    print(f"queue_upsert_issue should_terminate: {upsert_should_terminate}")
    
    print("\n=== Signaling consume_issues completion ===")
    await coordination_manager.signal_consume_issues_complete("test_consumer")
    
    print(f"consume_issues_complete: {coordination_manager.is_consume_issues_complete()}")
    print(f"are_all_specialized_consumers_done: {coordination_manager.are_all_specialized_consumers_done()}")
    
    for queue_name in specialized_queues:
        should_terminate = await coordination_manager.should_terminate_consumer(queue_name)
        print(f"{queue_name} should_terminate: {should_terminate}")
    
    upsert_should_terminate = await coordination_manager.should_terminate_consumer("queue_upsert_issue")
    print(f"queue_upsert_issue should_terminate: {upsert_should_terminate}")
    
    print("\n=== Final State ===")
    active_consumers = {}
    for queue_name in specialized_queues:
        count = len(coordination_manager.active_consumers[queue_name])
        if count > 0:
            active_consumers[queue_name] = coordination_manager.active_consumers[queue_name]
    
    if active_consumers:
        print(f"Still active consumers: {active_consumers}")
    else:
        print("All consumers cleaned up successfully!")

if __name__ == "__main__":
    asyncio.run(test_consumer_cleanup())
